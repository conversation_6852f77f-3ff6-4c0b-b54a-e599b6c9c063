package it.masterzen.blockbreak;

import it.masterzen.Keys.ItemList;
import it.masterzen.Keys.TemporaryReward;
import it.masterzen.MongoDB.PlayerData;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import org.apache.commons.lang3.EnumUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Consumer;

/**
 * Represents an async key opening task that processes keys in batches.
 * Maintains thread safety by executing Bukkit API calls on the main thread.
 */
public class KeyOpeningTask implements Runnable {

    private final Plugin plugin;
    private final UUID playerId;
    private final String playerName;
    private final String keyType;
    private final int totalKeys;
    private final int batchSize;
    private final long batchDelay;
    private final boolean sendMessage;
    private final List<ItemList> itemList;
    private final Consumer<KeyOpeningResult> resultCallback;
    private final KeyOpeningConfig config;

    private volatile boolean cancelled = false;
    private int processedKeys = 0;

    public KeyOpeningTask(Plugin plugin, Player player, String keyType, int totalKeys,
                         List<ItemList> itemList, boolean sendMessage, KeyOpeningConfig config,
                         Consumer<KeyOpeningResult> resultCallback) {
        this.plugin = plugin;
        this.playerId = player.getUniqueId();
        this.playerName = player.getName();
        this.keyType = keyType;
        this.totalKeys = totalKeys;
        this.batchSize = config.getBatchSize();
        this.batchDelay = config.getBatchDelay();
        this.sendMessage = sendMessage;
        this.itemList = itemList;
        this.resultCallback = resultCallback;
        this.config = config;
    }

    @Override
    public void run() {
        if (cancelled) {
            return;
        }

        try {
            // Process keys in batches to prevent server overload
            processKeysInBatches();

        } catch (Exception e) {
            plugin.getLogger().warning("Error processing key opening task for player " + playerName + ": " + e.getMessage());

            // Send error result
            if (resultCallback != null) {
                KeyOpeningResult errorResult = KeyOpeningResult.error("Processing failed: " + e.getMessage());
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        resultCallback.accept(errorResult);
                    }
                }.runTask(plugin);
            }
        }
    }

    /**
     * Processes keys in configurable batches with delays between batches.
     */
    private void processKeysInBatches() {
        int remainingKeys = totalKeys;
        int currentBatch = 0;

        // Process keys in batches
        while (remainingKeys > 0 && !cancelled) {
            int keysInThisBatch = Math.min(batchSize, remainingKeys);

            // Process this batch
            KeyOpeningBatchResult batchResult = processBatch(keysInThisBatch);

            // Update progress
            processedKeys += keysInThisBatch;
            remainingKeys -= keysInThisBatch;
            currentBatch++;

            // Send progress update if enabled
            if (config.isProgressFeedbackEnabled() && totalKeys >= config.getMinKeysForProgress()) {
                sendProgressUpdate(processedKeys, totalKeys);
            }

            // Apply batch results on main thread
            applyBatchResultsOnMainThread(batchResult, remainingKeys == 0);

            // Add delay between batches if there are more keys to process
            if (remainingKeys > 0 && batchDelay > 0) {
                try {
                    Thread.sleep(batchDelay);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // Send completion result
        if (resultCallback != null && !cancelled) {
            KeyOpeningResult result = KeyOpeningResult.success(processedKeys);
            new BukkitRunnable() {
                @Override
                public void run() {
                    resultCallback.accept(result);
                }
            }.runTask(plugin);
        }
    }

    /**
     * Processes a single batch of keys and calculates rewards.
     */
    private KeyOpeningBatchResult processBatch(int keysInBatch) {
        KeyOpeningBatchResult result = new KeyOpeningBatchResult();

        for (int i = 0; i < keysInBatch && !cancelled; i++) {
            boolean rewardAdded = false;
            int maxIteration = itemList.size() * 1000;

            while (!rewardAdded && maxIteration > 0) {
                double chance = ThreadLocalRandom.current().nextDouble(100);
                ItemList tmpItem = itemList.get(ThreadLocalRandom.current().nextInt(itemList.size()));

                if (tmpItem.getChance() > chance) {
                    processReward(tmpItem, result);
                    rewardAdded = true;
                }
                maxIteration--;
            }
        }

        return result;
    }

    /**
     * Processes a single reward and adds it to the batch result.
     */
    private void processReward(ItemList tmpItem, KeyOpeningBatchResult result) {
        if (tmpItem.getCommandToExecute().contains("giveTokenBooster")) {
            String tmp = tmpItem.getCommandToExecute();
            tmp = ChatColor.stripColor(tmp);
            tmp = tmp.replace("giveTokenBooster", "");
            tmp = tmp.trim();
            double tokensToAdd = Double.parseDouble(tmp);
            result.addTokens(tokensToAdd);
        } else if (tmpItem.getCommandToExecute().contains("giveMoneyBooster")) {
            String tmp = tmpItem.getCommandToExecute();
            tmp = ChatColor.stripColor(tmp);
            tmp = tmp.replace("giveMoneyBooster", "");
            tmp = tmp.trim();
            double moneyToAdd = Double.parseDouble(tmp);
            result.addMoney(moneyToAdd);
        } else if (tmpItem.getCommandToExecute().contains("giveXP")) {
            String tmp = tmpItem.getCommandToExecute();
            tmp = ChatColor.stripColor(tmp);
            tmp = tmp.replace("giveXP", "");
            tmp = tmp.trim();
            int xpToAdd = Integer.parseInt(tmp);
            result.addXP(xpToAdd);
        } else if (tmpItem.getCommandToExecute().contains("givePrestigePoints")) {
            String tmp = tmpItem.getCommandToExecute();
            tmp = ChatColor.stripColor(tmp);
            tmp = tmp.replace("givePrestigePoints", "");
            tmp = tmp.trim();
            int pointsToAdd = Integer.parseInt(tmp);
            result.addPrestigePoints(pointsToAdd);
        } else if (tmpItem.getCommandToExecute().contains("giveRobots")) {
            String tmp = tmpItem.getCommandToExecute();
            tmp = ChatColor.stripColor(tmp);
            tmp = tmp.replace("giveRobots", "");
            tmp = tmp.trim();
            int pointsToAdd = Integer.parseInt(tmp);
            result.addRobots(pointsToAdd);
        } else if (tmpItem.getCommandToExecute().contains("givePex")) {
            String tmp = tmpItem.getCommandToExecute();
            tmp = ChatColor.stripColor(tmp);
            tmp = tmp.replace("givePex ", "");
            result.addPex(tmp, tmpItem.getMessageToSend());
        } else {
            String command = tmpItem.getCommandToExecute().replace("player", playerName);
            String[] tmp = command.split(" ");
            int tmpAmount = Integer.parseInt(tmp[tmp.length - 1]);
            result.addCommand(command, tmpItem.getMessageToSend(), tmpAmount);
        }
    }

    /**
     * Applies batch results on the main thread to ensure thread safety.
     */
    private void applyBatchResultsOnMainThread(KeyOpeningBatchResult batchResult, boolean isLastBatch) {
        new BukkitRunnable() {
            @Override
            public void run() {
                Player player = Bukkit.getPlayer(playerId);
                if (player == null || !player.isOnline()) {
                    return;
                }

                AlphaBlockBreak mainClass = (AlphaBlockBreak) plugin;

                // Apply money booster
                if (batchResult.getTotalMoney() > 0) {
                    double totalMoney = batchResult.getTotalMoney();
                    if (player.hasPermission("pickreset.keysboosters")) {
                        totalMoney = totalMoney * 1.5;
                    }
                    PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                    data.setKeysMoneyBooster((data.getKeysMoneyBooster() == null ? 0 : data.getKeysMoneyBooster()) + totalMoney);
                    mainClass.getMongoReader().setPlayerMoneyBoosterInMap(player, data.getKeysMoneyBooster());

                    if (sendMessage && isLastBatch) {
                        player.sendMessage("§7+ §e§l" + mainClass.newFormatNumber(totalMoney) + "%§7 Money Booster §7§o(" + mainClass.newFormatNumber(data.getKeysMoneyBooster()) + "%)");
                    }
                }

                // Apply token booster
                if (batchResult.getTotalTokens() > 0) {
                    double totalTokens = batchResult.getTotalTokens();
                    if (player.hasPermission("pickreset.keysboosters")) {
                        totalTokens = totalTokens * 1.5;
                    }
                    PlayerData data = mainClass.getMongoReader().getPlayerData(player.getUniqueId());
                    data.setKeysTokenBooster((data.getKeysTokenBooster() == null ? 0 : data.getKeysTokenBooster()) + totalTokens);
                    mainClass.getMongoReader().setPlayerTokenBoosterInMap(player, data.getKeysTokenBooster());

                    if (sendMessage && isLastBatch) {
                        player.sendMessage("§7+ §a§l" + mainClass.newFormatNumber(totalTokens) + "%§7 Token Booster §7§o(" + mainClass.newFormatNumber(data.getKeysTokenBooster()) + "%)");
                    }
                }

                // Apply XP
                if (batchResult.getTotalXP() > 0) {
                    int totalXP = batchResult.getTotalXP();
                    String playerClassString = mainClass.getMongoReader().getPlayerData(player.getUniqueId()).getClassCode();
                    if (EnumUtils.isValidEnum(Enums.Classes.class, playerClassString)) {
                        Enums.Classes playerClass = Enums.Classes.valueOf(playerClassString);
                        if (playerClass == Enums.Classes.EXPERIENCER) {
                            totalXP = (int) (totalXP * playerClass.getBooster());
                        }
                    }

                    player.giveExp(totalXP);
                    if (sendMessage && isLastBatch) {
                        player.sendMessage("§7+ §b§l" + mainClass.newFormatNumber(totalXP) + "§7 XP");
                    }
                }

                // Apply prestige points
                if (batchResult.getTotalPrestigePoints() > 0) {
                    mainClass.getPrestigePoints().addPrestigePoints(player, batchResult.getTotalPrestigePoints());
                    if (sendMessage && isLastBatch) {
                        player.sendMessage("§7+ §a§l" + mainClass.newFormatNumber(batchResult.getTotalPrestigePoints()) + "§7 Prestige Points");
                    }
                }

                // Apply robots
                if (batchResult.getTotalRobots() > 0) {
                    int tier = ThreadLocalRandom.current().nextInt(3) + 1;
                    mainClass.getRobotSystem().addPoints(player, tier, batchResult.getTotalRobots());
                    if (sendMessage && isLastBatch) {
                        player.sendMessage("§7+ §a§l" + mainClass.newFormatNumber(batchResult.getTotalRobots()) + "§7 Robots");
                    }
                }

                // Apply permissions
                if (!batchResult.getPexToAdd().isEmpty()) {
                    User user = mainClass.getLuckPerms().getPlayerAdapter(Player.class).getUser(player);
                    for (String pex : batchResult.getPexToAdd().keySet()) {
                        if (player.hasPermission(pex)) {
                            it.masterzen.CustomArmor.ItemList armor = mainClass.getArmorSystem().getArmorFromPex(pex);
                            String armorRarity = ChatColor.stripColor(armor.getRarity());
                            if (armorRarity.contains("UNCOMMON")) {
                                mainClass.getArmorPointsSystem().addPoints(player, 25);
                                if (sendMessage && isLastBatch) {
                                    player.sendMessage("§e§lKEYS §8»§7 " + armor.getName() + " §7Duplicate. You received 25 ArmorPoints");
                                }
                            } else if (armorRarity.contains("RARE")) {
                                mainClass.getArmorPointsSystem().addPoints(player, 50);
                                if (sendMessage && isLastBatch) {
                                    player.sendMessage("§e§lKEYS §8»§7 " + armor.getName() + " §7Duplicate. You received 50 ArmorPoints");
                                }
                            } else if (armorRarity.contains("EPIC")) {
                                mainClass.getArmorPointsSystem().addPoints(player, 75);
                                if (sendMessage && isLastBatch) {
                                    player.sendMessage("§e§lKEYS §8»§7 " + armor.getName() + " §7Duplicate. You received 75 ArmorPoints");
                                }
                            }
                        } else if (sendMessage && isLastBatch) {
                            player.sendMessage(batchResult.getPexToAdd().get(pex));
                        }
                        Node node = Node.builder(pex).build();
                        user.data().add(node);
                        mainClass.getLuckPerms().getUserManager().saveUser(user);
                    }
                }

                // Execute commands
                for (String command : batchResult.getFinalRewards().keySet()) {
                    TemporaryReward reward = batchResult.getFinalRewards().get(command);
                    int tmpAmount = reward.getAmount();
                    String[] splittedCommand = reward.getCommandToExecute().split(" ");
                    StringBuilder finalCommand = new StringBuilder();
                    for (int i = 0; i < splittedCommand.length - 1; i++) {
                        finalCommand.append(splittedCommand[i]).append(" ");
                    }
                    finalCommand.append(tmpAmount);

                    if (command.contains("giveKey")) {
                        String tmpType = splittedCommand[1];
                        mainClass.getKeysManager().giveKeys(player, tmpType, tmpAmount, false);
                    } else {
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), finalCommand.toString());
                    }
                    if (sendMessage && isLastBatch) {
                        player.sendMessage("§7+ " + reward.getMessageToSend().replace("XXX", String.valueOf(tmpAmount)));
                    }
                }
            }
        }.runTask(plugin);
    }

    /**
     * Sends progress update to the player.
     */
    private void sendProgressUpdate(int processed, int total) {
        new BukkitRunnable() {
            @Override
            public void run() {
                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    int percentage = (int) ((double) processed / total * 100);
                    String progressBar = Utils.getProgressBar(processed, total, 20, '█', "§a", "§7");
                    player.sendMessage("§e§lKEYS §8»§7 Opening " + keyType + " keys... " + progressBar + " §f" + percentage + "%");
                }
            }
        }.runTask(plugin);
    }

    /**
     * Cancels the task.
     */
    public void cancel() {
        this.cancelled = true;
    }

    /**
     * Checks if the task is cancelled.
     */
    public boolean isCancelled() {
        return cancelled;
    }

    /**
     * Gets the player ID.
     */
    public UUID getPlayerId() {
        return playerId;
    }

    /**
     * Gets the player name.
     */
    public String getPlayerName() {
        return playerName;
    }

    /**
     * Gets the key type.
     */
    public String getKeyType() {
        return keyType;
    }

    /**
     * Gets the total number of keys to process.
     */
    public int getTotalKeys() {
        return totalKeys;
    }

    /**
     * Gets the number of processed keys.
     */
    public int getProcessedKeys() {
        return processedKeys;
    }
}